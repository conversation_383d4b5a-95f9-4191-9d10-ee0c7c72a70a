---
version: 2.1

orbs:
  prometheus: prometheus/prometheus@0.17.1

executors:
  # This must match .promu.yml.
  golang:
    docker:
      - image: cimg/go:1.24

jobs:
  test:
    executor: golang

    steps:
      - prometheus/setup_environment
      - run: make
      - prometheus/store_artifact:
          file: chrony_exporter

workflows:
  version: 2
  chrony_exporter:
    jobs:
      - test:
          filters:
            tags:
              only: /.*/
      - prometheus/build:
          name: build
          parallelism: 3
          filters:
            tags:
              only: /.*/
      - prometheus/publish_main:
          context: publish
          docker_hub_organization: superque
          quay_io_organization: superq
          requires:
            - test
            - build
          filters:
            branches:
              only: main
      - prometheus/publish_release:
          context: publish
          docker_hub_organization: superque
          quay_io_organization: superq
          requires:
            - test
            - build
          filters:
            tags:
              only: /^v.*/
            branches:
              ignore: /.*/
