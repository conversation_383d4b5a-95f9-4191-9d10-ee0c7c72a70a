---
name: Push README to Docker Hub
on:
  push:
    paths:
      - "README.md"
      - ".github/workflows/container_description.yml"
    branches: [ main, master ]

permissions:
  contents: read

jobs:
  PushDockerHubReadme:
    runs-on: ubuntu-latest
    name: Push README to Docker Hub
    if: github.repository_owner == 'prometheus' || github.repository_owner == 'prometheus-community' # Don't run this workflow on forks.
    steps:
      - name: git checkout
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
      - name: Push README to Dockerhub
        uses: christian-korneck/update-container-description-action@d36005551adeaba9698d8d67a296bd16fa91f8e8 # v1
        env:
          DOCKER_USER: ${{ secrets.DOCKER_HUB_LOGIN }}
          DOCKER_PASS: ${{ secrets.DOCKER_HUB_PASSWORD }}
          DOCKER_REPO_NAME: superque/chrony-exporter
        with:
          destination_container_repo: ${{ env.DOCKER_REPO_NAME }}
          provider: dockerhub
          short_description: ${{ env.DOCKER_REPO_NAME }}
          readme_file: 'README.md'

  PushQuayIoReadme:
    runs-on: ubuntu-latest
    name: Push README to quay.io
    if: github.repository_owner == 'prometheus' || github.repository_owner == 'prometheus-community' # Don't run this workflow on forks.
    steps:
      - name: git checkout
        uses: actions/checkout@b4ffde65f46336ab88eb53be808477a3936bae11 # v4.1.1
      - name: Push README to quay.io
        uses: christian-korneck/update-container-description-action@d36005551adeaba9698d8d67a296bd16fa91f8e8 # v1
        env:
          DOCKER_APIKEY: ${{ secrets.QUAY_IO_API_TOKEN }}
          DOCKER_REPO_NAME: superq/chrony-exporter
        with:
          destination_container_repo: ${{ env.DOCKER_REPO_NAME }}
          provider: quay
          readme_file: 'README.md'
