version: "2"
linters:
  enable:
    - misspell
    - revive
    - sloglint
  settings:
    revive:
      rules:
        - name: unused-parameter
          severity: warning
          disabled: true
  exclusions:
    generated: lax
    presets:
      - comments
      - common-false-positives
      - legacy
      - std-error-handling
    paths:
      - third_party$
      - builtin$
      - examples$
formatters:
  exclusions:
    generated: lax
    paths:
      - third_party$
      - builtin$
      - examples$
