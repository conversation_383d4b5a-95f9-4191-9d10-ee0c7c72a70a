---
# This action is synced from https://github.com/prometheus/prometheus
name: golangci-lint
on:
  push:
    paths:
      - "go.sum"
      - "go.mod"
      - "**.go"
      - "scripts/errcheck_excludes.txt"
      - ".github/workflows/golangci-lint.yml"
      - ".golangci.yml"
  pull_request:

permissions:  # added using https://github.com/step-security/secure-repo
  contents: read

jobs:
  golangci:
    permissions:
      contents: read  # for actions/checkout to fetch code
      pull-requests: read  # for golangci/golangci-lint-action to fetch pull requests
    name: lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - name: Install Go
        uses: actions/setup-go@0aaccfd150d50ccaeb58ebd88d36e91967a5f35b # v5.4.0
        with:
          go-version: 1.24.x
      - name: Install snmp_exporter/generator dependencies
        run: sudo apt-get update && sudo apt-get -y install libsnmp-dev
        if: github.repository == 'prometheus/snmp_exporter'
      - name: Lint
        uses: golangci/golangci-lint-action@1481404843c368bc19ca9406f87d6e0fc97bdcfd # v7.0.0
        with:
          args: --verbose
          version: v2.1.5
